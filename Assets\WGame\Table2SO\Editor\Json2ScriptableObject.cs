using System;
using System.IO;
using UnityEditor;
using UnityEngine;

public class Json2ScriptableObject
{
    public static string ExcelDir = "../ExcelUI/Excels";
    [MenuItem("Tools/Tabel2SO", priority = 0)]
    public static void Execute()
    {
        Table2Json();
    }

    private static void Table2Json()
    {
        var excelDir = Path.Join(Application.dataPath, ExcelDir);
        var startInfo = new System.Diagnostics.ProcessStartInfo();
        startInfo.WorkingDirectory = excelDir;
        startInfo.UseShellExecute = false;
        startInfo.RedirectStandardOutput = true;
        startInfo.RedirectStandardError = true;

        if (Application.platform == RuntimePlatform.WindowsEditor)
        {
            string batFilePath = Path.Combine(excelDir, "export.bat");
            if (!File.Exists(batFilePath))
            {
                Debug.LogError("export.bat file not found in " + excelDir);
                return;
            }
            startInfo.FileName = batFilePath;
            startInfo.Arguments = GameConfig.GetVer();
        }
        else if (Application.platform == RuntimePlatform.OSXEditor)
        {
            // Using absolute path for node, as Unity may not have the same PATH as the shell
            string command = "if [ -d 'json' ]; then rm -rf 'json'; fi && mkdir 'json' && /usr/local/bin/node index.js --export";
            startInfo.FileName = "/bin/sh";
            startInfo.Arguments = $"-c \"{command}\"";
        }
        else
        {
            Debug.LogError($"Unsupported editor platform: {Application.platform}");
            return;
        }

        var process = new System.Diagnostics.Process();
        process.StartInfo = startInfo;
        try
        {
            var output = new System.Text.StringBuilder();
            var error = new System.Text.StringBuilder();
            
            process.OutputDataReceived += (sender, args) => { if(args.Data != null) output.AppendLine(args.Data); };
            process.ErrorDataReceived += (sender, args) => { if(args.Data != null) error.AppendLine(args.Data); };

            process.Start();
            
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            process.WaitForExit();

            if (process.ExitCode == 0)
            {
                Debug.Log("Process executed successfully.\nOutput:\n" + output.ToString());
                Json2SO();
            }
            else
            {
                Debug.LogError($"Process exited with code {process.ExitCode}.\nOutput:\n{output.ToString()}\nError:\n{error.ToString()}");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Error occurred during process execution: " + ex.Message);
        }
    }

    private static void Json2SO()
    {
        var tables = TableSO.tableNames;
        string outputDir = "Assets/_MyGame/Bundles/DataSO/";

        // 确保输出目录存在
        if (!Directory.Exists(outputDir))
        {
            Directory.CreateDirectory(outputDir);
        }

        // 删除旧的单一TableSO文件
        string oldAssetPath = "Assets/_MyGame/Bundles/DataSO/TableSO.asset";
        if (File.Exists(oldAssetPath))
        {
            File.Delete(oldAssetPath);
        }

        // 为每个表格创建独立的ScriptableObject
        for (int i = 0; i < tables.Length; i++)
        {
            var tableName = tables[i];
            string jsonPath = Path.Join(Application.dataPath, $"{ExcelDir}/json", $"{tableName}.json");

            if (!File.Exists(jsonPath))
            {
                Debug.LogWarning($"JSON文件不存在: {jsonPath}");
                continue;
            }

            string text = File.ReadAllText(jsonPath);
            var json = LitJson.JsonMapper.ToObject(text);

            // 创建对应的ScriptableObject
            var tableData = CreateTableDataSO(tableName, json);
            if (tableData != null)
            {
                string assetPath = $"{outputDir}{tableName}SO.asset";

                // 删除已存在的asset
                if (File.Exists(assetPath))
                {
                    AssetDatabase.DeleteAsset(assetPath);
                }

                AssetDatabase.CreateAsset(tableData, assetPath);
                EditorUtility.SetDirty(tableData);
                Debug.Log($"创建表格资源: {assetPath}");
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("======表格导出完成======");
    }

    private static ScriptableObject CreateTableDataSO(string tableName, LitJson.JsonData json)
    {
        return tableName switch
        {
            "Equip" => CreateTableSO<InfoEquip>(json),
            "GateTemplate" => CreateTableSO<InfoGateTemplate>(json),
            "Monster" => CreateTableSO<InfoMonster>(json),
            "Weapon" => CreateTableSO<InfoWeapon>(json),
            "Bullet" => CreateTableSO<InfoBullet>(json),
            "Level" => CreateTableSO<InfoLevel>(json),
            "DropItem" => CreateTableSO<InfoDropItem>(json),
            "Map" => CreateTableSO<InfoMap>(json),
            "Buff" => CreateTableSO<InfoBuff>(json),
            "Item" => CreateTableSO<InfoItem>(json),
            "Langui" => CreateTableSO<InfoLang>(json),
            "LangError" => CreateTableSO<InfoLang>(json),
            "Skill" => CreateTableSO<InfoSkill>(json),
            "Entry" => CreateTableSO<InfoEntry>(json),
            "Guide" => CreateTableSO<InfoGuide>(json),
            "UserAgreement" => CreateTableSO<InfoUserAgreement>(json),
            "Share" => CreateTableSO<InfoShare>(json),
            "ResourceGuide" => CreateTableSO<InfoResourceGuide>(json),
            "GateMonsterShow" => CreateTableSO<InfoGateMonsterShow>(json),
            "HelpTips" => CreateTableSO<InfoHelpTip>(json),
            "BattleGuide" => CreateTableSO<InfoBattleGuide>(json),
            "BorderInfo" => CreateTableSO<InfoBorderInfo>(json),
            "CardFrame" => CreateTableSO<InfoCardFrame>(json),
            "FlyBulletBuffItem" => CreateTableSO<InfoFlyBulletBuffItem>(json),
            "FlyBulletMonsterWave" => CreateTableSO<InfoFlyBulletMonster>(json),
            "MidWayBuffItem" => CreateTableSO<InfoMidWayBuffItem>(json),
            "MidWayMonsterWave" => CreateTableSO<InfoMidWayMonster>(json),
            "TreasureRaidersBuffItem" => CreateTableSO<InfoTreasureRaidersBuffItem>(json),
            "TreasureRaidersMonsterWava" => CreateTableSO<InfoTreasureRaidersMonster>(json),
            "dragonMonsters" => CreateTableSO<InfoDragonMonster>(json),
            "dragonBuffItems" => CreateTableSO<InfoDragonBuffItem>(json),
            "DragonGearMonster" => CreateTableSO<InfoDragonGearMonster>(json),
            "DragonGearHero" => CreateTableSO<InfoDragonGearHero>(json),
            "DragonGearRate" => CreateTableSO<InfoDragonGearRate>(json),
            "hero_battle_skill_config" => CreateTableSO<InfoPVPSkill>(json),
            "hero_battle_buff_effect_config" => CreateTableSO<InfoPVPBuff>(json),
            "ChatSticker" => CreateTableSO<InfoSticker>(json),
            "ChatDeco" => CreateTableSO<InfoChatDeco>(json),
            _ => null,
        };
    }

    private static TableDataSO<T> CreateTableSO<T>(LitJson.JsonData json) where T : ConfigData, new()
    {
        var tableSO = ScriptableObject.CreateInstance<TableDataSO<T>>();
        tableSO.data = TableSO.CreateArray<T>(json);
        return tableSO;
    }
}