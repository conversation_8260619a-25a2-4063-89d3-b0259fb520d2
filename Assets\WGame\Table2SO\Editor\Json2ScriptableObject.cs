using System;
using System.IO;
using UnityEditor;
using UnityEngine;

public class Json2ScriptableObject
{
    public static string ExcelDir = "../ExcelUI/Excels";
    [MenuItem("Tools/Tabel2SO", priority = 0)]
    public static void Execute()
    {
        Table2Json();
    }

    private static void Table2Json()
    {
        var excelDir = Path.Join(Application.dataPath, ExcelDir);
        var startInfo = new System.Diagnostics.ProcessStartInfo();
        startInfo.WorkingDirectory = excelDir;
        startInfo.UseShellExecute = false;
        startInfo.RedirectStandardOutput = true;
        startInfo.RedirectStandardError = true;

        if (Application.platform == RuntimePlatform.WindowsEditor)
        {
            string batFilePath = Path.Combine(excelDir, "export.bat");
            if (!File.Exists(batFilePath))
            {
                Debug.LogError("export.bat file not found in " + excelDir);
                return;
            }
            startInfo.FileName = batFilePath;
            startInfo.Arguments = GameConfig.GetVer();
        }
        else if (Application.platform == RuntimePlatform.OSXEditor)
        {
            // Using absolute path for node, as Unity may not have the same PATH as the shell
            string command = "if [ -d 'json' ]; then rm -rf 'json'; fi && mkdir 'json' && /usr/local/bin/node index.js --export";
            startInfo.FileName = "/bin/sh";
            startInfo.Arguments = $"-c \"{command}\"";
        }
        else
        {
            Debug.LogError($"Unsupported editor platform: {Application.platform}");
            return;
        }

        var process = new System.Diagnostics.Process();
        process.StartInfo = startInfo;
        try
        {
            var output = new System.Text.StringBuilder();
            var error = new System.Text.StringBuilder();
            
            process.OutputDataReceived += (sender, args) => { if(args.Data != null) output.AppendLine(args.Data); };
            process.ErrorDataReceived += (sender, args) => { if(args.Data != null) error.AppendLine(args.Data); };

            process.Start();
            
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            process.WaitForExit();

            if (process.ExitCode == 0)
            {
                Debug.Log("Process executed successfully.\nOutput:\n" + output.ToString());
                Json2SO();
            }
            else
            {
                Debug.LogError($"Process exited with code {process.ExitCode}.\nOutput:\n{output.ToString()}\nError:\n{error.ToString()}");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Error occurred during process execution: " + ex.Message);
        }
    }

    private static void Json2SO()
    {
        var tables = TableSO.tableNames;
        string outputDir = "Assets/_MyGame/Bundles/DataSO/";

        // 确保输出目录存在
        if (!Directory.Exists(outputDir))
        {
            Directory.CreateDirectory(outputDir);
        }

        // 删除旧的单一TableSO文件
        string oldAssetPath = "Assets/_MyGame/Bundles/DataSO/TableSO.asset";
        if (File.Exists(oldAssetPath))
        {
            File.Delete(oldAssetPath);
        }

        // 为每个表格创建独立的ScriptableObject
        for (int i = 0; i < tables.Length; i++)
        {
            var tableName = tables[i];
            string jsonPath = Path.Join(Application.dataPath, $"{ExcelDir}/json", $"{tableName}.json");

            if (!File.Exists(jsonPath))
            {
                Debug.LogWarning($"JSON文件不存在: {jsonPath}");
                continue;
            }

            string text = File.ReadAllText(jsonPath);
            var json = LitJson.JsonMapper.ToObject(text);

            // 创建对应的ScriptableObject
            var tableData = CreateTableDataSO(tableName, json);
            if (tableData != null)
            {
                string assetPath = $"{outputDir}{tableName}SO.asset";

                // 删除已存在的asset
                if (File.Exists(assetPath))
                {
                    AssetDatabase.DeleteAsset(assetPath);
                }

                AssetDatabase.CreateAsset(tableData, assetPath);
                EditorUtility.SetDirty(tableData);
                Debug.Log($"创建表格资源: {assetPath}");
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("======表格导出完成======");
    }

    private static ScriptableObject CreateTableDataSO(string tableName, LitJson.JsonData json)
    {
        return tableName switch
        {
            "Equip" => CreateEquipSO(json),
            "GateTemplate" => CreateGateTemplateSO(json),
            "Monster" => CreateMonsterSO(json),
            "Weapon" => CreateWeaponSO(json),
            "Bullet" => CreateBulletSO(json),
            "Level" => CreateLevelSO(json),
            "DropItem" => CreateDropItemSO(json),
            "Map" => CreateMapSO(json),
            "Buff" => CreateBuffSO(json),
            "Item" => CreateItemSO(json),
            "Langui" => CreateLangSO(json),
            "LangError" => CreateLangSO(json),
            "Skill" => CreateSkillSO(json),
            "Entry" => CreateEntrySO(json),
            "Guide" => CreateGuideSO(json),
            "UserAgreement" => CreateUserAgreementSO(json),
            "Share" => CreateShareSO(json),
            "ResourceGuide" => CreateResourceGuideSO(json),
            "GateMonsterShow" => CreateGateMonsterShowSO(json),
            "HelpTips" => CreateHelpTipSO(json),
            "BattleGuide" => CreateBattleGuideSO(json),
            "BorderInfo" => CreateBorderInfoSO(json),
            "CardFrame" => CreateCardFrameSO(json),
            "FlyBulletBuffItem" => CreateFlyBulletBuffItemSO(json),
            "FlyBulletMonsterWave" => CreateFlyBulletMonsterSO(json),
            "MidWayBuffItem" => CreateMidWayBuffItemSO(json),
            "MidWayMonsterWave" => CreateMidWayMonsterSO(json),
            "TreasureRaidersBuffItem" => CreateTreasureRaidersBuffItemSO(json),
            "TreasureRaidersMonsterWava" => CreateTreasureRaidersMonsterSO(json),
            "dragonMonsters" => CreateDragonMonsterSO(json),
            "dragonBuffItems" => CreateDragonBuffItemSO(json),
            "DragonGearMonster" => CreateDragonGearMonsterSO(json),
            "DragonGearHero" => CreateDragonGearHeroSO(json),
            "DragonGearRate" => CreateDragonGearRateSO(json),
            "hero_battle_skill_config" => CreatePVPSkillSO(json),
            "hero_battle_buff_effect_config" => CreatePVPBuffSO(json),
            "ChatSticker" => CreateStickerSO(json),
            "ChatDeco" => CreateChatDecoSO(json),
            _ => null,
        };
    }

    // 具体的创建方法
    private static EquipSO CreateEquipSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<EquipSO>();
        so.data = TableSO.CreateArray<InfoEquip>(json);
        return so;
    }

    private static GateTemplateSO CreateGateTemplateSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<GateTemplateSO>();
        so.data = TableSO.CreateArray<InfoGateTemplate>(json);
        return so;
    }

    private static MonsterSO CreateMonsterSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<MonsterSO>();
        so.data = TableSO.CreateArray<InfoMonster>(json);
        return so;
    }

    private static WeaponSO CreateWeaponSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<WeaponSO>();
        so.data = TableSO.CreateArray<InfoWeapon>(json);
        return so;
    }

    private static BulletSO CreateBulletSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<BulletSO>();
        so.data = TableSO.CreateArray<InfoBullet>(json);
        return so;
    }

    private static LevelSO CreateLevelSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<LevelSO>();
        so.data = TableSO.CreateArray<InfoLevel>(json);
        return so;
    }

    private static DropItemSO CreateDropItemSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<DropItemSO>();
        so.data = TableSO.CreateArray<InfoDropItem>(json);
        return so;
    }

    private static MapSO CreateMapSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<MapSO>();
        so.data = TableSO.CreateArray<InfoMap>(json);
        return so;
    }

    private static BuffSO CreateBuffSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<BuffSO>();
        so.data = TableSO.CreateArray<InfoBuff>(json);
        return so;
    }

    private static ItemSO CreateItemSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<ItemSO>();
        so.data = TableSO.CreateArray<InfoItem>(json);
        return so;
    }

    private static LangSO CreateLangSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<LangSO>();
        so.data = TableSO.CreateArray<InfoLang>(json);
        return so;
    }

    private static SkillSO CreateSkillSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<SkillSO>();
        so.data = TableSO.CreateArray<InfoSkill>(json);
        return so;
    }

    private static EntrySO CreateEntrySO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<EntrySO>();
        so.data = TableSO.CreateArray<InfoEntry>(json);
        return so;
    }

    private static GuideSO CreateGuideSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<GuideSO>();
        so.data = TableSO.CreateArray<InfoGuide>(json);
        return so;
    }

    private static UserAgreementSO CreateUserAgreementSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<UserAgreementSO>();
        so.data = TableSO.CreateArray<InfoUserAgreement>(json);
        return so;
    }

    private static ShareSO CreateShareSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<ShareSO>();
        so.data = TableSO.CreateArray<InfoShare>(json);
        return so;
    }

    private static ResourceGuideSO CreateResourceGuideSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<ResourceGuideSO>();
        so.data = TableSO.CreateArray<InfoResourceGuide>(json);
        return so;
    }

    private static GateMonsterShowSO CreateGateMonsterShowSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<GateMonsterShowSO>();
        so.data = TableSO.CreateArray<InfoGateMonsterShow>(json);
        return so;
    }

    private static HelpTipSO CreateHelpTipSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<HelpTipSO>();
        so.data = TableSO.CreateArray<InfoHelpTip>(json);
        return so;
    }

    private static BattleGuideSO CreateBattleGuideSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<BattleGuideSO>();
        so.data = TableSO.CreateArray<InfoBattleGuide>(json);
        return so;
    }

    private static BorderInfoSO CreateBorderInfoSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<BorderInfoSO>();
        so.data = TableSO.CreateArray<InfoBorderInfo>(json);
        return so;
    }

    private static CardFrameSO CreateCardFrameSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<CardFrameSO>();
        so.data = TableSO.CreateArray<InfoCardFrame>(json);
        return so;
    }

    private static MidWayBuffItemSO CreateMidWayBuffItemSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<MidWayBuffItemSO>();
        so.data = TableSO.CreateArray<InfoMidWayBuffItem>(json);
        return so;
    }

    private static MidWayMonsterSO CreateMidWayMonsterSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<MidWayMonsterSO>();
        so.data = TableSO.CreateArray<InfoMidWayMonster>(json);
        return so;
    }

    private static TreasureRaidersBuffItemSO CreateTreasureRaidersBuffItemSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<TreasureRaidersBuffItemSO>();
        so.data = TableSO.CreateArray<InfoTreasureRaidersBuffItem>(json);
        return so;
    }

    private static TreasureRaidersMonsterSO CreateTreasureRaidersMonsterSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<TreasureRaidersMonsterSO>();
        so.data = TableSO.CreateArray<InfoTreasureRaidersMonster>(json);
        return so;
    }

    private static FlyBulletBuffItemSO CreateFlyBulletBuffItemSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<FlyBulletBuffItemSO>();
        so.data = TableSO.CreateArray<InfoFlyBulletBuffItem>(json);
        return so;
    }

    private static FlyBulletMonsterSO CreateFlyBulletMonsterSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<FlyBulletMonsterSO>();
        so.data = TableSO.CreateArray<InfoFlyBulletMonster>(json);
        return so;
    }

    private static DragonMonsterSO CreateDragonMonsterSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<DragonMonsterSO>();
        so.data = TableSO.CreateArray<InfoDragonMonster>(json);
        return so;
    }

    private static DragonBuffItemSO CreateDragonBuffItemSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<DragonBuffItemSO>();
        so.data = TableSO.CreateArray<InfoDragonBuffItem>(json);
        return so;
    }

    private static DragonGearMonsterSO CreateDragonGearMonsterSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<DragonGearMonsterSO>();
        so.data = TableSO.CreateArray<InfoDragonGearMonster>(json);
        return so;
    }

    private static DragonGearHeroSO CreateDragonGearHeroSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<DragonGearHeroSO>();
        so.data = TableSO.CreateArray<InfoDragonGearHero>(json);
        return so;
    }

    private static DragonGearRateSO CreateDragonGearRateSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<DragonGearRateSO>();
        so.data = TableSO.CreateArray<InfoDragonGearRate>(json);
        return so;
    }

    private static PVPSkillSO CreatePVPSkillSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<PVPSkillSO>();
        so.data = TableSO.CreateArray<InfoPVPSkill>(json);
        return so;
    }

    private static PVPBuffSO CreatePVPBuffSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<PVPBuffSO>();
        so.data = TableSO.CreateArray<InfoPVPBuff>(json);
        return so;
    }

    private static StickerSO CreateStickerSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<StickerSO>();
        so.data = TableSO.CreateArray<InfoSticker>(json);
        return so;
    }

    private static ChatDecoSO CreateChatDecoSO(LitJson.JsonData json)
    {
        var so = ScriptableObject.CreateInstance<ChatDecoSO>();
        so.data = TableSO.CreateArray<InfoChatDeco>(json);
        return so;
    }
}