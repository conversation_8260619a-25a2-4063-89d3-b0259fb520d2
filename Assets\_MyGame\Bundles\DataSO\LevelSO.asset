%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 363770c8c78eb7747a2b85830a2187a0, type: 3}
  m_Name: LevelSO
  m_EditorClassIdentifier: 
  data:
  - uniqueKey: 1
    level: 1
    exp: 50
  - uniqueKey: 2
    level: 2
    exp: 100
  - uniqueKey: 3
    level: 3
    exp: 200
  - uniqueKey: 4
    level: 4
    exp: 300
  - uniqueKey: 5
    level: 5
    exp: 400
  - uniqueKey: 6
    level: 6
    exp: 500
  - uniqueKey: 7
    level: 7
    exp: 600
  - uniqueKey: 8
    level: 8
    exp: 700
  - uniqueKey: 9
    level: 9
    exp: 800
  - uniqueKey: 10
    level: 10
    exp: 1000
  - uniqueKey: 11
    level: 11
    exp: 1250
  - uniqueKey: 12
    level: 12
    exp: 1500
  - uniqueKey: 13
    level: 13
    exp: 1750
  - uniqueKey: 14
    level: 14
    exp: 2000
  - uniqueKey: 15
    level: 15
    exp: 2250
  - uniqueKey: 16
    level: 16
    exp: 2500
  - uniqueKey: 17
    level: 17
    exp: 2750
  - uniqueKey: 18
    level: 18
    exp: 3000
  - uniqueKey: 19
    level: 19
    exp: 3250
  - uniqueKey: 20
    level: 20
    exp: 3500
  - uniqueKey: 21
    level: 21
    exp: 3750
  - uniqueKey: 22
    level: 22
    exp: 4000
  - uniqueKey: 23
    level: 23
    exp: 4250
  - uniqueKey: 24
    level: 24
    exp: 4500
  - uniqueKey: 25
    level: 25
    exp: 4750
  - uniqueKey: 26
    level: 26
    exp: 5000
  - uniqueKey: 27
    level: 27
    exp: 5250
  - uniqueKey: 28
    level: 28
    exp: 5500
