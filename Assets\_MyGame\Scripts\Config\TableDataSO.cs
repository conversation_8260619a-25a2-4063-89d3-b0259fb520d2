using UnityEngine;
using System;

// 基础抽象类
public abstract class TableDataSOBase : ScriptableObject
{
    public abstract ConfigData[] GetDataArray();
}

// 泛型基类
[Serializable]
public abstract class TableDataSO<T> : TableDataSOBase where T : ConfigData
{
    public T[] data;

    public T[] GetData()
    {
        return data;
    }

    public override ConfigData[] GetDataArray()
    {
        return data;
    }
}
