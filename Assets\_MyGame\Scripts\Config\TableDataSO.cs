using UnityEngine;
using System;

// 基础抽象类
public abstract class TableDataSOBase : ScriptableObject
{
    public abstract ConfigData[] GetDataArray();
}

// 泛型基类
[Serializable]
public abstract class TableDataSO<T> : TableDataSOBase where T : ConfigData
{
    public T[] data;

    public T[] GetData()
    {
        return data;
    }

    public override ConfigData[] GetDataArray()
    {
        return data;
    }
}

// 具体的表格ScriptableObject类
[CreateAssetMenu(fileName = "EquipSO", menuName = "TableData/EquipSO")]
public class EquipSO : TableDataSO<InfoEquip> { }

[CreateAssetMenu(fileName = "GateTemplateSO", menuName = "TableData/GateTemplateSO")]
public class GateTemplateSO : TableDataSO<InfoGateTemplate> { }

[CreateAssetMenu(fileName = "MonsterSO", menuName = "TableData/MonsterSO")]
public class MonsterSO : TableDataSO<InfoMonster> { }

[CreateAssetMenu(fileName = "WeaponSO", menuName = "TableData/WeaponSO")]
public class WeaponSO : TableDataSO<InfoWeapon> { }

[CreateAssetMenu(fileName = "BulletSO", menuName = "TableData/BulletSO")]
public class BulletSO : TableDataSO<InfoBullet> { }

[CreateAssetMenu(fileName = "LevelSO", menuName = "TableData/LevelSO")]
public class LevelSO : TableDataSO<InfoLevel> { }

[CreateAssetMenu(fileName = "DropItemSO", menuName = "TableData/DropItemSO")]
public class DropItemSO : TableDataSO<InfoDropItem> { }

[CreateAssetMenu(fileName = "MapSO", menuName = "TableData/MapSO")]
public class MapSO : TableDataSO<InfoMap> { }

[CreateAssetMenu(fileName = "BuffSO", menuName = "TableData/BuffSO")]
public class BuffSO : TableDataSO<InfoBuff> { }

[CreateAssetMenu(fileName = "ItemSO", menuName = "TableData/ItemSO")]
public class ItemSO : TableDataSO<InfoItem> { }

[CreateAssetMenu(fileName = "LangSO", menuName = "TableData/LangSO")]
public class LangSO : TableDataSO<InfoLang> { }

[CreateAssetMenu(fileName = "SkillSO", menuName = "TableData/SkillSO")]
public class SkillSO : TableDataSO<InfoSkill> { }

[CreateAssetMenu(fileName = "EntrySO", menuName = "TableData/EntrySO")]
public class EntrySO : TableDataSO<InfoEntry> { }

[CreateAssetMenu(fileName = "GuideSO", menuName = "TableData/GuideSO")]
public class GuideSO : TableDataSO<InfoGuide> { }

[CreateAssetMenu(fileName = "UserAgreementSO", menuName = "TableData/UserAgreementSO")]
public class UserAgreementSO : TableDataSO<InfoUserAgreement> { }

[CreateAssetMenu(fileName = "ShareSO", menuName = "TableData/ShareSO")]
public class ShareSO : TableDataSO<InfoShare> { }

[CreateAssetMenu(fileName = "ResourceGuideSO", menuName = "TableData/ResourceGuideSO")]
public class ResourceGuideSO : TableDataSO<InfoResourceGuide> { }

[CreateAssetMenu(fileName = "GateMonsterShowSO", menuName = "TableData/GateMonsterShowSO")]
public class GateMonsterShowSO : TableDataSO<InfoGateMonsterShow> { }

[CreateAssetMenu(fileName = "HelpTipSO", menuName = "TableData/HelpTipSO")]
public class HelpTipSO : TableDataSO<InfoHelpTip> { }

[CreateAssetMenu(fileName = "BattleGuideSO", menuName = "TableData/BattleGuideSO")]
public class BattleGuideSO : TableDataSO<InfoBattleGuide> { }

[CreateAssetMenu(fileName = "BorderInfoSO", menuName = "TableData/BorderInfoSO")]
public class BorderInfoSO : TableDataSO<InfoBorderInfo> { }

[CreateAssetMenu(fileName = "CardFrameSO", menuName = "TableData/CardFrameSO")]
public class CardFrameSO : TableDataSO<InfoCardFrame> { }

[CreateAssetMenu(fileName = "MidWayBuffItemSO", menuName = "TableData/MidWayBuffItemSO")]
public class MidWayBuffItemSO : TableDataSO<InfoMidWayBuffItem> { }

[CreateAssetMenu(fileName = "MidWayMonsterSO", menuName = "TableData/MidWayMonsterSO")]
public class MidWayMonsterSO : TableDataSO<InfoMidWayMonster> { }

[CreateAssetMenu(fileName = "TreasureRaidersBuffItemSO", menuName = "TableData/TreasureRaidersBuffItemSO")]
public class TreasureRaidersBuffItemSO : TableDataSO<InfoTreasureRaidersBuffItem> { }

[CreateAssetMenu(fileName = "TreasureRaidersMonsterSO", menuName = "TableData/TreasureRaidersMonsterSO")]
public class TreasureRaidersMonsterSO : TableDataSO<InfoTreasureRaidersMonster> { }

[CreateAssetMenu(fileName = "FlyBulletBuffItemSO", menuName = "TableData/FlyBulletBuffItemSO")]
public class FlyBulletBuffItemSO : TableDataSO<InfoFlyBulletBuffItem> { }

[CreateAssetMenu(fileName = "FlyBulletMonsterSO", menuName = "TableData/FlyBulletMonsterSO")]
public class FlyBulletMonsterSO : TableDataSO<InfoFlyBulletMonster> { }

[CreateAssetMenu(fileName = "DragonMonsterSO", menuName = "TableData/DragonMonsterSO")]
public class DragonMonsterSO : TableDataSO<InfoDragonMonster> { }

[CreateAssetMenu(fileName = "DragonBuffItemSO", menuName = "TableData/DragonBuffItemSO")]
public class DragonBuffItemSO : TableDataSO<InfoDragonBuffItem> { }

[CreateAssetMenu(fileName = "DragonGearMonsterSO", menuName = "TableData/DragonGearMonsterSO")]
public class DragonGearMonsterSO : TableDataSO<InfoDragonGearMonster> { }

[CreateAssetMenu(fileName = "DragonGearHeroSO", menuName = "TableData/DragonGearHeroSO")]
public class DragonGearHeroSO : TableDataSO<InfoDragonGearHero> { }

[CreateAssetMenu(fileName = "DragonGearRateSO", menuName = "TableData/DragonGearRateSO")]
public class DragonGearRateSO : TableDataSO<InfoDragonGearRate> { }

[CreateAssetMenu(fileName = "PVPSkillSO", menuName = "TableData/PVPSkillSO")]
public class PVPSkillSO : TableDataSO<InfoPVPSkill> { }

[CreateAssetMenu(fileName = "PVPBuffSO", menuName = "TableData/PVPBuffSO")]
public class PVPBuffSO : TableDataSO<InfoPVPBuff> { }

[CreateAssetMenu(fileName = "StickerSO", menuName = "TableData/StickerSO")]
public class StickerSO : TableDataSO<InfoSticker> { }

[CreateAssetMenu(fileName = "ChatDecoSO", menuName = "TableData/ChatDecoSO")]
public class ChatDecoSO : TableDataSO<InfoChatDeco> { }
