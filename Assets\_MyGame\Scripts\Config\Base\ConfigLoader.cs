using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class ConfigLoader
{
    public static Func<string, string> ProcessAssetbundlePath;

    private List<IConfigLoadData> dataList;
    private Action<float> callBack;

    public ConfigLoader()
    {
        dataList = new List<IConfigLoadData>();
    }

    public void Add<T1, T2>(string assetbundlePath, string assetbundleName = null) where T1 : ConfigManager, new() where T2 : ConfigData, new()
    {
        dataList.Add(new ConfigLoadData<T1, T2>(assetbundlePath, assetbundleName));
    }

    public int GetTotalCount()
    {
        return dataList.Count;
    }

    private int loadIdx;
    public void Load(Action<float> callBack)
    {
        loadIdx = 0;
        this.callBack = callBack;
        Dequeue();
    }

    private void Dequeue()
    {
        if (loadIdx < this.dataList.Count)
        {
            IConfigLoadData data = this.dataList[loadIdx];
            //this.dataList.RemoveAt(0);
            loadIdx++;
            data.Load(OnLoadAssetBundle);
        }
    }

    private void OnLoadAssetBundle(bool result)
    {
        this.LoadResult(false);
        if (result)
        {
            this.Dequeue();
        }

    }

    private void LoadResult(bool result)
    {
        this.callBack?.Invoke((float)loadIdx/(float)GetTotalCount());
    }
}

public class ConfigLoadData<T1, T2> : IConfigLoadData where T1 : ConfigManager, new() where T2 : ConfigData, new()
{
    public string assetbundlePath;
    public string assetbundleName;

    public ConfigLoadData(string path, string name)
    {
        assetbundlePath = path;
        assetbundleName = name;
    }

    public void Load(Action<bool> callBack)
    {
        var path = assetbundlePath;
        if (ConfigLoader.ProcessAssetbundlePath != null)
        {
            path = ConfigLoader.ProcessAssetbundlePath(assetbundlePath);
        }
        ConfigHelper.GetManager<T1>().LoadAssetBundle<T2>(path, assetbundleName, callBack);
    }
}

public interface IConfigLoadData
{
    void Load(Action<bool> callBack);
}
