using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Xml;
using LitJson;
using System;

[Serializable]

public class ConfigData
{

    protected XmlElement xml;
    protected JsonData json;
    public string uniqueKey;
    public virtual object key { get; protected set; }


    public virtual void Init(XmlElement xml)
    {
        this.xml = xml;
	}

    public virtual void Init(object key, JsonData json)
    {
        this.key = key;
        this.json = json;
        Parse(json);
    }

    public virtual void Parse(JsonData data)
    {

    }
}


