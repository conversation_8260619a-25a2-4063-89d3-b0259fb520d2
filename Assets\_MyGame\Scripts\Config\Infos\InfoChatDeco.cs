using System;
using LitJson;

[Serializable]
public class InfoChatDeco : ConfigData
{
    public int decoID;
    public string resName;
    public string textColor;

    public override object key { get => decoID; protected set => base.key = value; }

    public override void Parse(JsonData data)
    {
        decoID = JsonUtil.ToInt(data, "decoId");
        resName = JsonUtil.ToString(data, "resName");
        textColor = JsonUtil.ToString(data, "textColor");
    }
}