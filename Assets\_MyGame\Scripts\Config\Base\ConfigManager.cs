using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Xml;
using LitJson;
using System;
using UnityEngine.Profiling;
using UnityEngine.Assertions;

public class ConfigManager
{

    protected Dictionary<object, ConfigData> dataDictList;

    public ConfigManager()
    {
        dataDictList = new Dictionary<object, ConfigData>();
    }

    public virtual void LoadAssetBundle<T>(string assetBundlePath, string assetBundleName, Action<bool> loadedCallBack) where T : ConfigData, new()
    {
        // 新的独立表格加载方式
        string tablePath = $"DataSO/{assetBundleName}SO";
        AssetBundleManager.LoadObject(tablePath, (obj) =>
        {
            Profiler.BeginSample("readTable:" + assetBundleName);

            if (obj is TableDataSO<T> tableSO)
            {
                var data = tableSO.GetData();
                Assert.IsNotNull(data, assetBundleName + ":table data is null");
                InitByJson<T>(data);
            }
            else
            {
                Debug.LogError($"加载表格失败，类型不匹配: {tablePath}, 期望类型: TableDataSO<{typeof(T).Name}>");
                loadedCallBack?.Invoke(false);
                return;
            }

            Profiler.EndSample();
            loadedCallBack?.Invoke(true);
        });

        // 保留旧的TableSO加载方式作为备用
        // AssetBundleManager.LoadObject("DataSO/TableSO", (obj) =>
        // {
        //     Profiler.BeginSample("readTable:" + assetBundleName);
        //     var tableSo = obj as TableSO;
        //     var data = tableSo.GetDatas(assetBundleName);
        //
        //     Assert.IsNotNull(data, assetBundleName + ":table data is null");
        //     InitByJson<T>(data);
        //     Profiler.EndSample();
        //     loadedCallBack?.Invoke(true);
        // });
    }

    public virtual void InitByJson<T>(ConfigData[] json) where T : ConfigData, new()
    {
        dataDictList.Clear();
        for (int i = 0; i < json.Length; i++)
        {
            var data = json[i];
            AddData(data);
        }
    }

    public virtual void InitByJson<T>(JsonData json) where T : ConfigData, new()
    {
        dataDictList.Clear();

        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Init(i, json[i]);
                AddData(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Init(key, json[key]);
                AddData(data);
            }
        }
    }

    public virtual void InitByXML<T>(string xmlPath) where T : ConfigData, new()
    {
        dataDictList.Clear();

        TextAsset textAsset = (TextAsset)Resources.Load(xmlPath);

        XmlDocument xml = new XmlDocument();
        xml.LoadXml(textAsset.text);

        XmlNode mapNode = xml.SelectSingleNode("data");

        XmlNodeList nodeList = mapNode.SelectNodes("item");
        for (int i = 0; i < nodeList.Count; i++)
        {
            T data = new T();
            data.Init((XmlElement)nodeList[i]);
            AddData(data);
        }
    }

    protected virtual void AddData(ConfigData data)
    {
        if (!dataDictList.ContainsKey(data.uniqueKey))
            dataDictList.Add(data.uniqueKey, data);
        else
            Debug.LogError(data.GetType().ToString() + " : " + data.key + " has duplicate");
    }

    public virtual T GetData<T>(object id) where T : ConfigData
    {
        dataDictList.TryGetValue(id.ToString(), out ConfigData value);
        return (T)value;
    }

    public virtual List<T> GetDataList<T>() where T : ConfigData
    {
        List<T> result = new List<T>();
        foreach (var item in dataDictList)
        {
            result.Add((T)item.Value);
        }
        return result;
    }
}


public class ConfigHelper
{
    private static Dictionary<int, ConfigManager> dataManagerDict;
    public static T GetManager<T>() where T : ConfigManager, new()
    {
        if (dataManagerDict == null)
            dataManagerDict = new Dictionary<int, ConfigManager>();

        ConfigManager dataManager = null;
        int key = typeof(T).GetHashCode();

        dataManagerDict.TryGetValue(key, out dataManager);

        if (dataManager == null)
        {
            dataManager = new T();
            dataManagerDict.Add(key, dataManager);
        }
        return (T)dataManager;
    }
}





