%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 05024a545cd834847a8d0429fb2a5aeb, type: 3}
  m_Name: FlyBulletMonsterWave
  m_EditorClassIdentifier: 
  tableName: FlyBulletMonsterWave
  configDataArray:
  - uniqueKey: 30000
  - uniqueKey: 30001
  - uniqueKey: 30002
  - uniqueKey: 30003
  - uniqueKey: 30004
  - uniqueKey: 30005
  - uniqueKey: 30006
  - uniqueKey: 30007
  - uniqueKey: 30008
  - uniqueKey: 30009
  - uniqueKey: 30010
  - uniqueKey: 30011
  - uniqueKey: 30012
  - uniqueKey: 30100
  - uniqueKey: 30101
  - uniqueKey: 30102
  - uniqueKey: 30103
  - uniqueKey: 30104
  - uniqueKey: 30105
  - uniqueKey: 30106
  - uniqueKey: 30107
  - uniqueKey: 30108
  - uniqueKey: 30109
  - uniqueKey: 30110
  - uniqueKey: 30111
  - uniqueKey: 30112
  - uniqueKey: 30113
  - uniqueKey: 30114
  - uniqueKey: 30115
  - uniqueKey: 30116
  - uniqueKey: 30117
  - uniqueKey: 30200
  - uniqueKey: 30201
  - uniqueKey: 30202
  - uniqueKey: 30203
  - uniqueKey: 30204
  - uniqueKey: 30205
  - uniqueKey: 30206
  - uniqueKey: 30207
  - uniqueKey: 30208
  - uniqueKey: 30209
  - uniqueKey: 30210
  - uniqueKey: 30211
  - uniqueKey: 30212
  - uniqueKey: 30213
  - uniqueKey: 30214
  - uniqueKey: 30215
  - uniqueKey: 30216
  - uniqueKey: 30217
  - uniqueKey: 30218
  - uniqueKey: 30219
  - uniqueKey: 30220
  - uniqueKey: 30221
  - uniqueKey: 30222
  - uniqueKey: 30300
  - uniqueKey: 30301
  - uniqueKey: 30302
  - uniqueKey: 30303
  - uniqueKey: 30304
  - uniqueKey: 30305
  - uniqueKey: 30306
  - uniqueKey: 30307
  - uniqueKey: 30308
  - uniqueKey: 30309
  - uniqueKey: 30310
  - uniqueKey: 30311
  - uniqueKey: 30312
  - uniqueKey: 30313
  - uniqueKey: 30314
  - uniqueKey: 30315
  - uniqueKey: 30316
  - uniqueKey: 30317
  - uniqueKey: 30318
  - uniqueKey: 30319
  - uniqueKey: 30320
  - uniqueKey: 30321
  - uniqueKey: 30322
  - uniqueKey: 30400
  - uniqueKey: 30401
  - uniqueKey: 30402
  - uniqueKey: 30403
  - uniqueKey: 30404
  - uniqueKey: 30405
  - uniqueKey: 30406
  - uniqueKey: 30407
  - uniqueKey: 30408
  - uniqueKey: 30409
  - uniqueKey: 30410
  - uniqueKey: 30411
  - uniqueKey: 30412
  - uniqueKey: 30413
  - uniqueKey: 30414
  - uniqueKey: 30415
  - uniqueKey: 30416
  - uniqueKey: 30417
  - uniqueKey: 30418
  - uniqueKey: 30419
  - uniqueKey: 30420
  - uniqueKey: 30421
  - uniqueKey: 30422
  - uniqueKey: 30423
  - uniqueKey: 30424
  - uniqueKey: 30425
  - uniqueKey: 30426
  - uniqueKey: 30427
  - uniqueKey: 30500
  - uniqueKey: 30501
  - uniqueKey: 30502
  - uniqueKey: 30503
  - uniqueKey: 30504
  - uniqueKey: 30505
  - uniqueKey: 30506
  - uniqueKey: 30507
  - uniqueKey: 30508
  - uniqueKey: 30509
  - uniqueKey: 30510
  - uniqueKey: 30511
  - uniqueKey: 30512
  - uniqueKey: 30513
  - uniqueKey: 30514
  - uniqueKey: 30515
  - uniqueKey: 30516
  - uniqueKey: 30517
  - uniqueKey: 30518
  - uniqueKey: 30519
  - uniqueKey: 30520
  - uniqueKey: 30521
  - uniqueKey: 30522
  - uniqueKey: 30523
  - uniqueKey: 30524
  - uniqueKey: 30525
  - uniqueKey: 30526
  - uniqueKey: 30527
  - uniqueKey: 30528
  - uniqueKey: 30529
  - uniqueKey: 30530
  - uniqueKey: 30531
  - uniqueKey: 30532
  - uniqueKey: 30600
  - uniqueKey: 30601
  - uniqueKey: 30602
  - uniqueKey: 30603
  - uniqueKey: 30604
  - uniqueKey: 30605
  - uniqueKey: 30606
  - uniqueKey: 30607
  - uniqueKey: 30608
  - uniqueKey: 30609
  - uniqueKey: 30610
  - uniqueKey: 30611
  - uniqueKey: 30612
  - uniqueKey: 30613
  - uniqueKey: 30614
  - uniqueKey: 30615
  - uniqueKey: 30616
  - uniqueKey: 30617
  - uniqueKey: 30618
  - uniqueKey: 30619
  - uniqueKey: 30620
  - uniqueKey: 30621
  - uniqueKey: 30622
  - uniqueKey: 30623
  - uniqueKey: 30624
  - uniqueKey: 30625
  - uniqueKey: 30626
  - uniqueKey: 30627
  - uniqueKey: 30628
  - uniqueKey: 30629
  - uniqueKey: 30630
  - uniqueKey: 30631
  - uniqueKey: 30632
  - uniqueKey: 30700
  - uniqueKey: 30701
  - uniqueKey: 30702
  - uniqueKey: 30703
  - uniqueKey: 30704
  - uniqueKey: 30705
  - uniqueKey: 30706
  - uniqueKey: 30707
  - uniqueKey: 30708
  - uniqueKey: 30709
  - uniqueKey: 30710
  - uniqueKey: 30711
  - uniqueKey: 30712
  - uniqueKey: 30713
  - uniqueKey: 30714
  - uniqueKey: 30715
  - uniqueKey: 30716
  - uniqueKey: 30717
  - uniqueKey: 30718
  - uniqueKey: 30719
  - uniqueKey: 30720
  - uniqueKey: 30721
  - uniqueKey: 30722
  - uniqueKey: 30723
  - uniqueKey: 30724
  - uniqueKey: 30725
  - uniqueKey: 30726
  - uniqueKey: 30727
  - uniqueKey: 30728
  - uniqueKey: 30729
  - uniqueKey: 30730
  - uniqueKey: 30731
  - uniqueKey: 30732
  - uniqueKey: 30800
  - uniqueKey: 30801
  - uniqueKey: 30802
  - uniqueKey: 30803
  - uniqueKey: 30804
  - uniqueKey: 30805
  - uniqueKey: 30806
  - uniqueKey: 30807
  - uniqueKey: 30808
  - uniqueKey: 30809
  - uniqueKey: 30810
  - uniqueKey: 30811
  - uniqueKey: 30812
  - uniqueKey: 30813
  - uniqueKey: 30814
  - uniqueKey: 30815
  - uniqueKey: 30816
  - uniqueKey: 30817
  - uniqueKey: 30818
  - uniqueKey: 30819
  - uniqueKey: 30820
  - uniqueKey: 30821
  - uniqueKey: 30822
  - uniqueKey: 30823
  - uniqueKey: 30824
  - uniqueKey: 30825
  - uniqueKey: 30826
  - uniqueKey: 30827
  - uniqueKey: 30828
  - uniqueKey: 30829
  - uniqueKey: 30830
  - uniqueKey: 30831
  - uniqueKey: 30832
  - uniqueKey: 30900
  - uniqueKey: 30901
  - uniqueKey: 30902
  - uniqueKey: 30903
  - uniqueKey: 30904
  - uniqueKey: 30905
  - uniqueKey: 30906
  - uniqueKey: 30907
  - uniqueKey: 30908
  - uniqueKey: 30909
  - uniqueKey: 30910
  - uniqueKey: 30911
  - uniqueKey: 30912
  - uniqueKey: 30913
  - uniqueKey: 30914
  - uniqueKey: 30915
  - uniqueKey: 30916
  - uniqueKey: 30917
  - uniqueKey: 30918
  - uniqueKey: 30919
  - uniqueKey: 30920
  - uniqueKey: 30921
  - uniqueKey: 30922
  - uniqueKey: 30923
  - uniqueKey: 30924
  - uniqueKey: 30925
  - uniqueKey: 30926
  - uniqueKey: 30927
  - uniqueKey: 30928
  - uniqueKey: 30929
  - uniqueKey: 30930
  - uniqueKey: 30931
  - uniqueKey: 30932
  - uniqueKey: 31000
  - uniqueKey: 31001
  - uniqueKey: 31002
  - uniqueKey: 31003
  - uniqueKey: 31004
  - uniqueKey: 31005
  - uniqueKey: 31006
  - uniqueKey: 31007
  - uniqueKey: 31008
  - uniqueKey: 31009
  - uniqueKey: 31010
  - uniqueKey: 31011
  - uniqueKey: 31012
  - uniqueKey: 31013
  - uniqueKey: 31014
  - uniqueKey: 31015
  - uniqueKey: 31016
  - uniqueKey: 31017
  - uniqueKey: 31018
  - uniqueKey: 31019
  - uniqueKey: 31020
  - uniqueKey: 31021
  - uniqueKey: 31022
  - uniqueKey: 31023
  - uniqueKey: 31024
  - uniqueKey: 31025
  - uniqueKey: 31026
  - uniqueKey: 31027
  - uniqueKey: 31028
  - uniqueKey: 31029
  - uniqueKey: 31030
  - uniqueKey: 31031
  - uniqueKey: 31032
  - uniqueKey: 31100
  - uniqueKey: 31101
  - uniqueKey: 31102
  - uniqueKey: 31103
  - uniqueKey: 31104
  - uniqueKey: 31105
  - uniqueKey: 31106
  - uniqueKey: 31107
  - uniqueKey: 31108
  - uniqueKey: 31109
  - uniqueKey: 31110
  - uniqueKey: 31111
  - uniqueKey: 31112
  - uniqueKey: 31113
  - uniqueKey: 31114
  - uniqueKey: 31115
  - uniqueKey: 31116
  - uniqueKey: 31117
  - uniqueKey: 31118
  - uniqueKey: 31119
  - uniqueKey: 31120
  - uniqueKey: 31121
  - uniqueKey: 31122
  - uniqueKey: 31123
  - uniqueKey: 31124
  - uniqueKey: 31125
  - uniqueKey: 31126
  - uniqueKey: 31127
  - uniqueKey: 31128
  - uniqueKey: 31129
  - uniqueKey: 31130
  - uniqueKey: 31131
  - uniqueKey: 31132
  - uniqueKey: 31200
  - uniqueKey: 31201
  - uniqueKey: 31202
  - uniqueKey: 31203
  - uniqueKey: 31204
  - uniqueKey: 31205
  - uniqueKey: 31206
  - uniqueKey: 31207
  - uniqueKey: 31208
  - uniqueKey: 31209
  - uniqueKey: 31210
  - uniqueKey: 31211
  - uniqueKey: 31212
  - uniqueKey: 31213
  - uniqueKey: 31214
  - uniqueKey: 31215
  - uniqueKey: 31216
  - uniqueKey: 31217
  - uniqueKey: 31218
  - uniqueKey: 31219
  - uniqueKey: 31220
  - uniqueKey: 31221
  - uniqueKey: 31222
  - uniqueKey: 31223
  - uniqueKey: 31224
  - uniqueKey: 31225
  - uniqueKey: 31226
  - uniqueKey: 31227
  - uniqueKey: 31228
  - uniqueKey: 31229
  - uniqueKey: 31230
  - uniqueKey: 31231
  - uniqueKey: 31232
  - uniqueKey: 31300
  - uniqueKey: 31301
  - uniqueKey: 31302
  - uniqueKey: 31303
  - uniqueKey: 31304
  - uniqueKey: 31305
  - uniqueKey: 31306
  - uniqueKey: 31307
  - uniqueKey: 31308
  - uniqueKey: 31309
  - uniqueKey: 31310
  - uniqueKey: 31311
  - uniqueKey: 31312
  - uniqueKey: 31313
  - uniqueKey: 31314
  - uniqueKey: 31315
  - uniqueKey: 31316
  - uniqueKey: 31317
  - uniqueKey: 31318
  - uniqueKey: 31319
  - uniqueKey: 31320
  - uniqueKey: 31321
  - uniqueKey: 31322
  - uniqueKey: 31323
  - uniqueKey: 31324
  - uniqueKey: 31325
  - uniqueKey: 31326
  - uniqueKey: 31327
  - uniqueKey: 31328
  - uniqueKey: 31329
  - uniqueKey: 31330
  - uniqueKey: 31331
  - uniqueKey: 31332
  - uniqueKey: 31400
  - uniqueKey: 31401
  - uniqueKey: 31402
  - uniqueKey: 31403
  - uniqueKey: 31404
  - uniqueKey: 31405
  - uniqueKey: 31406
  - uniqueKey: 31407
  - uniqueKey: 31408
  - uniqueKey: 31409
  - uniqueKey: 31410
  - uniqueKey: 31411
  - uniqueKey: 31412
  - uniqueKey: 31413
  - uniqueKey: 31414
  - uniqueKey: 31415
  - uniqueKey: 31416
  - uniqueKey: 31417
  - uniqueKey: 31418
  - uniqueKey: 31419
  - uniqueKey: 31420
  - uniqueKey: 31421
  - uniqueKey: 31422
  - uniqueKey: 31423
  - uniqueKey: 31424
  - uniqueKey: 31425
  - uniqueKey: 31426
  - uniqueKey: 31427
  - uniqueKey: 31428
  - uniqueKey: 31429
  - uniqueKey: 31430
  - uniqueKey: 31431
  - uniqueKey: 31432
  - uniqueKey: 31500
  - uniqueKey: 31501
  - uniqueKey: 31502
  - uniqueKey: 31503
  - uniqueKey: 31504
  - uniqueKey: 31505
  - uniqueKey: 31506
  - uniqueKey: 31507
  - uniqueKey: 31508
  - uniqueKey: 31509
  - uniqueKey: 31510
  - uniqueKey: 31511
  - uniqueKey: 31512
  - uniqueKey: 31513
  - uniqueKey: 31514
  - uniqueKey: 31515
  - uniqueKey: 31516
  - uniqueKey: 31517
  - uniqueKey: 31518
  - uniqueKey: 31519
  - uniqueKey: 31520
  - uniqueKey: 31521
  - uniqueKey: 31522
  - uniqueKey: 31523
  - uniqueKey: 31524
  - uniqueKey: 31525
  - uniqueKey: 31526
  - uniqueKey: 31527
  - uniqueKey: 31528
  - uniqueKey: 31529
  - uniqueKey: 31530
  - uniqueKey: 31531
  - uniqueKey: 31532
  - uniqueKey: 31600
  - uniqueKey: 31601
  - uniqueKey: 31602
  - uniqueKey: 31603
  - uniqueKey: 31604
  - uniqueKey: 31605
  - uniqueKey: 31606
  - uniqueKey: 31607
  - uniqueKey: 31608
  - uniqueKey: 31609
  - uniqueKey: 31610
  - uniqueKey: 31611
  - uniqueKey: 31612
  - uniqueKey: 31613
  - uniqueKey: 31614
  - uniqueKey: 31615
  - uniqueKey: 31616
  - uniqueKey: 31617
  - uniqueKey: 31618
  - uniqueKey: 31619
  - uniqueKey: 31620
  - uniqueKey: 31621
  - uniqueKey: 31622
  - uniqueKey: 31623
  - uniqueKey: 31624
  - uniqueKey: 31625
  - uniqueKey: 31626
  - uniqueKey: 31627
  - uniqueKey: 31628
  - uniqueKey: 31629
  - uniqueKey: 31630
  - uniqueKey: 31631
  - uniqueKey: 31632
  - uniqueKey: 31700
  - uniqueKey: 31701
  - uniqueKey: 31702
  - uniqueKey: 31703
  - uniqueKey: 31704
  - uniqueKey: 31705
  - uniqueKey: 31706
  - uniqueKey: 31707
  - uniqueKey: 31708
  - uniqueKey: 31709
  - uniqueKey: 31710
  - uniqueKey: 31711
  - uniqueKey: 31712
  - uniqueKey: 31713
  - uniqueKey: 31714
  - uniqueKey: 31715
  - uniqueKey: 31716
  - uniqueKey: 31717
  - uniqueKey: 31718
  - uniqueKey: 31719
  - uniqueKey: 31720
  - uniqueKey: 31721
  - uniqueKey: 31722
  - uniqueKey: 31723
  - uniqueKey: 31724
  - uniqueKey: 31725
  - uniqueKey: 31726
  - uniqueKey: 31727
  - uniqueKey: 31728
  - uniqueKey: 31729
  - uniqueKey: 31730
  - uniqueKey: 31731
  - uniqueKey: 31732
  - uniqueKey: 31800
  - uniqueKey: 31801
  - uniqueKey: 31802
  - uniqueKey: 31803
  - uniqueKey: 31804
  - uniqueKey: 31805
  - uniqueKey: 31806
  - uniqueKey: 31807
  - uniqueKey: 31808
  - uniqueKey: 31809
  - uniqueKey: 31810
  - uniqueKey: 31811
  - uniqueKey: 31812
  - uniqueKey: 31813
  - uniqueKey: 31814
  - uniqueKey: 31815
  - uniqueKey: 31816
  - uniqueKey: 31817
  - uniqueKey: 31818
  - uniqueKey: 31819
  - uniqueKey: 31820
  - uniqueKey: 31821
  - uniqueKey: 31822
  - uniqueKey: 31823
  - uniqueKey: 31824
  - uniqueKey: 31825
  - uniqueKey: 31826
  - uniqueKey: 31827
  - uniqueKey: 31828
  - uniqueKey: 31829
  - uniqueKey: 31830
  - uniqueKey: 31831
  - uniqueKey: 31832
  - uniqueKey: 31900
  - uniqueKey: 31901
  - uniqueKey: 31902
  - uniqueKey: 31903
  - uniqueKey: 31904
  - uniqueKey: 31905
  - uniqueKey: 31906
  - uniqueKey: 31907
  - uniqueKey: 31908
  - uniqueKey: 31909
  - uniqueKey: 31910
  - uniqueKey: 31911
  - uniqueKey: 31912
  - uniqueKey: 31913
  - uniqueKey: 31914
  - uniqueKey: 31915
  - uniqueKey: 31916
  - uniqueKey: 31917
  - uniqueKey: 31918
  - uniqueKey: 31919
  - uniqueKey: 31920
  - uniqueKey: 31921
  - uniqueKey: 31922
  - uniqueKey: 31923
  - uniqueKey: 31924
  - uniqueKey: 31925
  - uniqueKey: 31926
  - uniqueKey: 31927
  - uniqueKey: 31928
  - uniqueKey: 31929
  - uniqueKey: 31930
  - uniqueKey: 31931
  - uniqueKey: 31932
  - uniqueKey: 32000
  - uniqueKey: 32001
  - uniqueKey: 32002
  - uniqueKey: 32003
  - uniqueKey: 32004
  - uniqueKey: 32005
  - uniqueKey: 32006
  - uniqueKey: 32007
  - uniqueKey: 32008
  - uniqueKey: 32009
  - uniqueKey: 32010
  - uniqueKey: 32011
  - uniqueKey: 32012
  - uniqueKey: 32013
  - uniqueKey: 32014
  - uniqueKey: 32015
  - uniqueKey: 32016
  - uniqueKey: 32017
  - uniqueKey: 32018
  - uniqueKey: 32019
  - uniqueKey: 32020
  - uniqueKey: 32021
  - uniqueKey: 32022
  - uniqueKey: 32023
  - uniqueKey: 32024
  - uniqueKey: 32025
  - uniqueKey: 32026
  - uniqueKey: 32027
  - uniqueKey: 32028
  - uniqueKey: 32029
  - uniqueKey: 32030
  - uniqueKey: 32031
  - uniqueKey: 32032
  - uniqueKey: 32100
  - uniqueKey: 32101
  - uniqueKey: 32102
  - uniqueKey: 32103
  - uniqueKey: 32104
  - uniqueKey: 32105
  - uniqueKey: 32106
  - uniqueKey: 32107
  - uniqueKey: 32108
  - uniqueKey: 32109
  - uniqueKey: 32110
  - uniqueKey: 32111
  - uniqueKey: 32112
  - uniqueKey: 32113
  - uniqueKey: 32114
  - uniqueKey: 32115
  - uniqueKey: 32116
  - uniqueKey: 32117
  - uniqueKey: 32118
  - uniqueKey: 32119
  - uniqueKey: 32120
  - uniqueKey: 32121
  - uniqueKey: 32122
  - uniqueKey: 32123
  - uniqueKey: 32124
  - uniqueKey: 32125
  - uniqueKey: 32126
  - uniqueKey: 32127
  - uniqueKey: 32128
  - uniqueKey: 32129
  - uniqueKey: 32130
  - uniqueKey: 32131
  - uniqueKey: 32132
  - uniqueKey: 32200
  - uniqueKey: 32201
  - uniqueKey: 32202
  - uniqueKey: 32203
  - uniqueKey: 32204
  - uniqueKey: 32205
  - uniqueKey: 32206
  - uniqueKey: 32207
  - uniqueKey: 32208
  - uniqueKey: 32209
  - uniqueKey: 32210
  - uniqueKey: 32211
  - uniqueKey: 32212
  - uniqueKey: 32213
  - uniqueKey: 32214
  - uniqueKey: 32215
  - uniqueKey: 32216
  - uniqueKey: 32217
  - uniqueKey: 32218
  - uniqueKey: 32219
  - uniqueKey: 32220
  - uniqueKey: 32221
  - uniqueKey: 32222
  - uniqueKey: 32223
  - uniqueKey: 32224
  - uniqueKey: 32225
  - uniqueKey: 32226
  - uniqueKey: 32227
  - uniqueKey: 32228
  - uniqueKey: 32229
  - uniqueKey: 32230
  - uniqueKey: 32231
  - uniqueKey: 32232
  - uniqueKey: 32300
  - uniqueKey: 32301
  - uniqueKey: 32302
  - uniqueKey: 32303
  - uniqueKey: 32304
  - uniqueKey: 32305
  - uniqueKey: 32306
  - uniqueKey: 32307
  - uniqueKey: 32308
  - uniqueKey: 32309
  - uniqueKey: 32310
  - uniqueKey: 32311
  - uniqueKey: 32312
  - uniqueKey: 32313
  - uniqueKey: 32314
  - uniqueKey: 32315
  - uniqueKey: 32316
  - uniqueKey: 32317
  - uniqueKey: 32318
  - uniqueKey: 32319
  - uniqueKey: 32320
  - uniqueKey: 32321
  - uniqueKey: 32322
  - uniqueKey: 32323
  - uniqueKey: 32324
  - uniqueKey: 32325
  - uniqueKey: 32326
  - uniqueKey: 32327
  - uniqueKey: 32328
  - uniqueKey: 32329
  - uniqueKey: 32330
  - uniqueKey: 32331
  - uniqueKey: 32332
  - uniqueKey: 32400
  - uniqueKey: 32401
  - uniqueKey: 32402
  - uniqueKey: 32403
  - uniqueKey: 32404
  - uniqueKey: 32405
  - uniqueKey: 32406
  - uniqueKey: 32407
  - uniqueKey: 32408
  - uniqueKey: 32409
  - uniqueKey: 32410
  - uniqueKey: 32411
  - uniqueKey: 32412
  - uniqueKey: 32413
  - uniqueKey: 32414
  - uniqueKey: 32415
  - uniqueKey: 32416
  - uniqueKey: 32417
  - uniqueKey: 32418
  - uniqueKey: 32419
  - uniqueKey: 32420
  - uniqueKey: 32421
  - uniqueKey: 32422
  - uniqueKey: 32423
  - uniqueKey: 32424
  - uniqueKey: 32425
  - uniqueKey: 32426
  - uniqueKey: 32427
  - uniqueKey: 32428
  - uniqueKey: 32429
  - uniqueKey: 32430
  - uniqueKey: 32431
  - uniqueKey: 32432
